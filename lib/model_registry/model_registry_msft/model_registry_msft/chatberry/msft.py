from model_registry.model_data import BerryModelData
from model_registry.model_feature import ENGINE_V3, ModelFeatures

# from personal/bolian/start-bus-o3.sh
O3_0402_410 = BerryModelData(
    name="o3_0402_410",
    external_name="o3",
    snapshot_path="az://orngscuscresco/models/snapshots/o3-0402-410-8shard-decrypted",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[
        "twppo.scallion.fp4moe8kv.sampling",
    ],
    extra_kwargs={
        "warmup_forward": False,
        "warmup_backward": False,
        "warmup_loss": False,
        "tensorcache_v2_limit_to_label": False,
        "enable_tensorcache_v2": True,
        "tensorcache_v2_indirect_load_threshold": 0,
        "ixf_batcher_type": "berry",
        "ixf_cpu_kv_cache_ratio": 0.0,
        "raise_on_load_for_missing_tensor": False,
        "allow_embedding_prefix_loading": True,
        "ixf_use_applied_comms": True,
        "twapi_use_per_pd_redis": True,
        "ixf_batch_send_mode": "redis_to_rank_zero_per_pd",
        "ixf_batcher_numa_bind": True,
        "ixf_batch_runner_numa_bind": True,
        "ixf_enable_presampling_mask_args_cache": True,
        "ixf_enable_batcher_full_pd_ahead_schedule": True,
        "image_encoder_type": "emb",
        "include_audio_embeddings": False,
        "ixf_max_cache_list_length": 4096,
        "ixf_kv_block_unit_size": 2048,
        "do_multimodal_projections_in_ixf_batch_runner": True,
    },
)


# from project/omom_msft/omom_msft/data/models_config.json
O3_MINI = BerryModelData(
    name="o3_mini",
    external_name="o3-mini",
    snapshot_path="az://orngcresco/models/snapshots/models.tc/neutrino-o1.5-mini-base-before-posttraining-transferred-20241219-decrypted",
    renderer_name="harmony_v4.0.15_berry_v3_16k_orion_text",
    extra_args=[],
    extra_kwargs={
        "raise_on_load_for_missing_tensor": False,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "enable_tensorcache_v2": False,
    },
)


# from project/omom_msft/omom_msft/data/models_config.json
O3_D64_STEP860 = BerryModelData(
    name="o3_d64_step860",
    external_name="o3",
    config_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal",
    snapshot_path="az://orngcresco/models/snapshots/o3-d64-step860-20250324-retransferred-20250401-decrypted",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[],
    extra_kwargs={
        "load_inference_snapshots_with_tcv2": False,
        "raise_on_load_for_missing_tensor": False,
        "ixf_sampling_extension_gpu_to_cpu_async": False,
        "twapi_cpu_comm_backend": "gloo",
        "ixf_batcher_response_loop_timeout": 600,
        "allow_embedding_prefix_loading": True,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "decoupled_attention_token_mapping": "{None:0,(0,200166):0}",
        "n_op_shards": 8,
    },
    features=ModelFeatures(
        is_multimodal=True,
        encoder_decoder_snapshots='{"clip":"az://orngcresco/models/snapshots/mm/oaiomni/rcall/crow-export/scallion2b/omni/model_final_ts.pt"}',
    ),
)


SWEBERRY_V2_NV4_FINAL = BerryModelData(
    name="sweberry_v2_nv4_final",
    external_name="sweberry_v2_nv4_final",
    snapshot_path="az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    renderer_name="harmony_v4.0.15_berry_v3_1mil_orion_lpe",
    extra_args=[
        "falcon.multimodal.runs.scallion-d36-s64-lpe",
        "twppo.scallion.text.common",
    ],
    extra_kwargs={
        "raise_on_load_for_missing_tensor": False,
        "enable_tensorcache_v2": False,
        "ixf_max_cache_list_length": 4096,
        "ixf_kv_block_unit_size": 2048,
        "n_op_shards": 2,
        "pipe_depth": 4,
    },
)


GPT_4O_MINI_TRANSFERRED = BerryModelData(
    name="gpt_4o_mini_transferred",
    external_name="gpt_4o_mini_transferred",
    snapshot_path="az://orngscuscresco/models/snapshots/4o-tt-v5-mini-transferred-2025-04-11-decrypted",
    renderer_name="harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action",
    extra_args=[],
    extra_kwargs={
        "do_multimodal_projections_in_ixf_batch_runner": False,
        "include_audio_embeddings": False,
        "decoupled_attn": False,
        "decoupled_attention_token_mapping": None,
        "ixf_batch_runner_memory_buffer": 21474836480,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "allow_embedding_prefix_loading": True,
        "ixf_max_cache_list_length": 4096,
        "ixf_kv_block_unit_size": 2048,
        "mem_disable_second_allocator": True,
    },
)


SWEBERRY_V2_MINI = BerryModelData(
    name="sweberry_v2_mini",
    external_name="sweberry_v2_mini",
    snapshot_path="az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    renderer_name="harmony_v4.0.15_berry_v3_1mil_orion_lpe",
    extra_args=[
        "falcon.multimodal.runs.scallion-d36-s64-lpe",
        "twppo.scallion.text.common",
    ],
    extra_kwargs={
        "raise_on_load_for_missing_tensor": False,
        "enable_tensorcache_v2": False,
        "ixf_max_cache_list_length": 4096,
        "ixf_kv_block_unit_size": 2048,
        "n_op_shards": 2,
        "pipe_depth": 4,
    },
)


GPT_4O_TTV3 = BerryModelData(
    name="gpt_4o_ttv3",
    external_name="gpt_4o_ttv3",
    snapshot_path="az://orngcresco/models/snapshots/chat-gpt-4o-tt-v3-d64_80g_bf16_fridge-step3075-250316-decrypted/",
    renderer_name="harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action",
    extra_args=[],
    extra_kwargs={
        "n_op_shards": 8,
        "pipe_depth": 1,
        "do_multimodal_projections_in_ixf_batch_runner": False,
        "enable_tensorcache_v2": False,
        "raise_on_load_for_missing_tensor": False,
        "allow_embedding_prefix_loading": True,
    },
)


GPT_4O = BerryModelData(
    name="gpt_4o",
    external_name="gpt_4o",
    snapshot_path="az://orngcresco/models/snapshots/4o-tt-v5-transferred-2025-04-09-decrypted/",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[],
    extra_kwargs={
        "load_inference_snapshots_with_tcv2": False,
        "raise_on_load_for_missing_tensor": False,
        "ixf_sampling_extension_gpu_to_cpu_async": False,
        "twapi_cpu_comm_backend": "gloo",
        "ixf_batcher_response_loop_timeout": 600,
        "allow_embedding_prefix_loading": True,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "decoupled_attention_token_mapping": "{None:0,(0,200166):0}",
    },
)


TBV3 = BerryModelData(
    name="tbv3",
    external_name="tbv3",
    snapshot_path="az://orngcresco/models/snapshots/tbv3p2-v1-step700-decrypted",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[],
    extra_kwargs={
        "load_inference_snapshots_with_tcv2": False,
        "raise_on_load_for_missing_tensor": False,
        "ixf_sampling_extension_gpu_to_cpu_async": False,
        "twapi_cpu_comm_backend": "gloo",
        "ixf_batcher_response_loop_timeout": 600,
        "allow_embedding_prefix_loading": True,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "decoupled_attention_token_mapping": "{None:0,(0,200166):0}",
    },
)


APPBERRY_0411 = BerryModelData(
    name="appberry_0411",
    external_name="appberry_0411",
    snapshot_path="az://orngcresco/models/snapshots/4o-tt-v5-mini-transferred-2025-04-11-decrypted",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_lpe_no_budget_commentary_cs",
    extra_args=[
        "falcon.multimodal.runs.scallion-d36-s64-lpe",
    ],
    extra_kwargs={
        "do_multimodal_projections_in_ixf_batch_runner": False,
        "includ_audio_embeddings": False,  # Note: typo in original config
        "decoupled_attn": False,
        "decoupled_attention_token_mapping": None,
        "ixf_batch_runner_memory_buffer": 21474836480,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "allow_embedding_prefix_loading": True,
        "ixf_max_cache_list_length": 4096,
        "ixf_kv_block_unit_size": 2048,
        "mem_disable_second_allocator": True,
        "n_op_shards": 2,
        "pipe_depth": 4,
    },
)

SWEBERRY_V2_D64 = BerryModelData(
    name="sweberry_v2_d64",
    external_name="sweberry_v2_d64",
    config_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal",
    snapshot_path="az://orngcresco/models/snapshots/sweberry-v2-tbv3-final2-20250216-decrypted/",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[],
    extra_kwargs={
        "load_inference_snapshots_with_tcv2": False,
        "raise_on_load_for_missing_tensor": False,
        "ixf_sampling_extension_gpu_to_cpu_async": False,
        "twapi_cpu_comm_backend": "gloo",
        "ixf_batcher_response_loop_timeout": 600,
        "allow_embedding_prefix_loading": True,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "decoupled_attention_token_mapping": "{None:0,(0,200166):0}",
    },
    features=ModelFeatures(
        is_multimodal=True,
        encoder_decoder_snapshots='{"clip":"az://orngcresco/models/snapshots/mm/oaiomni/rcall/crow-export/scallion2b/omni/model_final_ts.pt"}',
    ),
)

GPT_5_NANO_REASONING = BerryModelData(
    name="gpt_5_nano_reasoning",
    external_name="gpt-5-nano-reasoning",
    snapshot_path="az://orngcresco/models/gpt5n-s50-v4-re-2025-06-25-18-07-decrypted",
    renderer_name="harmony_v4.0.16_128k_orion_orion_200k_no_asr_16k_action_lpe",
    extra_args=[],
    extra_kwargs={
        "n_op_shards": 8,
        "pipe_depth": 1,
    },
    features=ModelFeatures.enable(ENGINE_V3),
)


GPT_5_MINI_REASONING = BerryModelData(
    name="gpt_5_mini_reasoning",
    external_name="gpt-5-mini-reasoning",
    snapshot_path="az://orngscuscresco/models/snapshots/zen-os4-nv4-cbv6-hh-rkld-4jul-orca-2-2025-07-10-00-04-decrypted",
    renderer_name="harmony_v4.0.15_berry_v3_1mil_orion_lpe",
    extra_args=[
        "falcon.multimodal.runs.scallion-d36-s64-lpe",
    ],
    extra_kwargs={
        "raise_on_load_for_missing_tensor": False,
        "n_op_shards": 8,
        "pipe_depth": 1,
    },
)


GPT_5_CHAT = BerryModelData(
    name="gpt_5_chat",
    external_name="gpt-5-chat",
    config_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal",
    snapshot_path="az://orngcresco/models/snapshots/scallion_d64-jj_arm10-1950-8shard-decrypted",
    renderer_name="harmony_v4.0.16_berry_v3_1mil_orion_no_budget",
    extra_args=[],
    extra_kwargs={
        "load_inference_snapshots_with_tcv2": False,
        "raise_on_load_for_missing_tensor": False,
        "ixf_sampling_extension_gpu_to_cpu_async": False,
        "twapi_cpu_comm_backend": "gloo",
        "ixf_batcher_response_loop_timeout": 600,
        "allow_embedding_prefix_loading": True,
        "tensorcache_v2_load_allow_missing_tensor": True,
        "decoupled_attention_token_mapping": "{None:0,(0,200166):0}",
    },
)


GPT_5_REASONING = BerryModelData(
    name="gpt_5_reasoning",
    external_name="gpt-5-reasoning",
    config_name="falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal",
    snapshot_path="az://orngscuscresco/models/snapshots/cb-research-oseries-d64-h3-ll-ulus-t3-530-8shard-2025-07-25-15-56-decrypted-rbiswas",
    renderer_name="harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action",
    extra_args=[],
    extra_kwargs={
        "tensorcache_v2_load_allow_missing_tensor": True,
        "decoupled_attention_token_mapping": "{(0,200166):0}",
        "allow_embedding_prefix_loading": True,
        "ixf_max_cache_list_length": 4096,
    },
)


GPT_5_SWITCHER = BerryModelData(
    name="gpt_5_switcher",
    external_name="gpt-5-switcher",
    snapshot_path="az://orngscuscresco/models/snapshots/scallion-d16-auto-switcher-0715-4565-1shard-decrypted/",
    renderer_name="harmony_v4.0.13_16k_orion_mmgen_no_asr_2k_action",
    extra_args=[],
    extra_kwargs={
        "n_op_shards": 2,
        "pipe_depth": 1,
        "softmax_xent_implementation": "triton",
    },
)
