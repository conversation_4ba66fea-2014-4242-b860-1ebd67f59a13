import asyncio
import json
import time

import blobfile as bf
import structlog
from caas.commands import BashScript, RawBashScript
from caas.terminal.api import TerminalSession
from deep_swe_msft.swe_bench_train_v2_vsc.setup.setup import swe_bench_v2_setup_fn
from deep_swe_msft.tools.vscode_copilot_tool import (
    exec,
    new_container,
    prepare_vsc_tool,
)
from smokey import Smokey

logger = structlog.stdlib.get_logger(component=__name__)
sem = asyncio.Semaphore(200)

async def extract_project_structure(endpoint, sample):
    caas_session = None
    try:
        async with sem:
            caas_container = await new_container(endpoint, sample["metadata"]["docker_image"])
            caas_session = caas_container.caas_session
            await prepare_vsc_tool(caas_session, sample["metadata"]["cwd"])
            terminal_session = TerminalSession(caas_session, endpoint=endpoint)
            await swe_bench_v2_setup_fn(datapoint=sample, terminal_session=terminal_session)
            project_structure, metadata = await exec(caas_session, "read_project_structure", {})
        assert "VSCode Copilot tool error:" not in project_structure, project_structure
        print(f"{project_structure=}")
        return project_structure
    except Exception as e:
        import traceback

        tb_str = traceback.format_exc()
        print(tb_str)
        print(sample["unique_id"])

        # import trainflow.utils; trainflow.utils.debugpy_breakpoint(remote=True)
        raise e
    finally:
        if caas_session is not None:
            try:
                await caas_session.close()
            except Exception as e:
                pass

failed = [
]


async def main():
    endpoint: str = "https://eastus2.caas.azure.com"
    with bf.BlobFile(
        "az://orngscuscresco/data/zhendongw/swe-bench-train-vsc/test_train/swe_bench_train_updated.jsonl",
        "r",
    ) as f:
        samples = [json.loads(line) for line in f]

    if failed:
        samples = [sample for sample in samples if sample["unique_id"] in failed]
    tasks = {
        sample["unique_id"]: asyncio.create_task(extract_project_structure(endpoint, sample))
        for sample in samples
    }

    results = await asyncio.gather(*tasks.values(), return_exceptions=True)
    read_project_structure_map = {uid: res for uid, res in zip(tasks.keys(), results)}

    failure_cnt = 0
    with bf.BlobFile(
        "az://orngscuscresco/data/luw/swe-bench-train-vsc/test_train/swe_bench_train_updated.jsonl",
        "a",
    ) as f:
        for sample in samples:
            project_structure = read_project_structure_map[sample["unique_id"]]
            if isinstance(project_structure, str):
                sample["metadata"]["project_structure"] = project_structure
                f.write(json.dumps(sample) + "\n")
            else:
                # import trainflow.utils; trainflow.utils.debugpy_breakpoint(remote=True)
                failure_cnt += 1
                print('"' + sample["unique_id"] + '",')

    print(f"Failed to extract project structure for {failure_cnt} samples out of {len(samples)}")


if __name__ == "__main__":
    asyncio.run(Smokey(main))
