# 🧠 OpenAI Proxy MSFT

一个基于 FastAPI 的 OpenAI 兼容代理服务，用于支持本地模型 API 调用。该服务提供标准的 OpenAI Chat Completions API 接口，内部使用 BusTokenCompleter 与运行在 Pod 中的本地模型引擎进行通信。

## 📋 项目描述 (Description)

OpenAI Proxy MSFT 是一个高性能的代理服务，旨在为各种 OpenAI 兼容的客户端提供无缝的本地模型访问能力。该服务的核心特性包括：

### 🎯 主要功能

- **OpenAI 兼容 API**: 完全兼容 OpenAI Chat Completions API v1 规范
- **本地模型支持**: 通过 BusTokenCompleter 连接本地运行的模型引擎
- **工具调用支持**: 支持 OpenAI 格式的 function calling 和 tool calls
- **流式响应**: 支持流式 (streaming) 和非流式响应模式
- **多模型配置**: 支持动态模型配置，格式为 `topic$renderer`
- **高性能架构**: 基于 FastAPI 和异步处理，支持高并发请求

### 🏗️ 系统架构

```
Client (OpenAI SDK) → OpenAI Proxy MSFT → BusTokenCompleter → Local Model Engines (Pods)
```

1. **客户端层**: 任何支持 OpenAI API 的客户端 (Python SDK, curl, 等)
2. **代理层**: OpenAI Proxy MSFT 服务，负责协议转换和请求路由
3. **消息处理层**: TokenMessageCompleter 负责消息格式转换和渲染
4. **总线层**: BusTokenCompleter 负责与模型引擎的通信
5. **模型层**: 运行在 Pod 中的实际模型引擎

### 🔧 核心组件

- **OpenAIProxy**: 主要的代理服务类，处理请求路由和组件管理
- **BusTokenCompleter**: 负责与本地模型引擎的通信，支持多种 QoS 策略
- **TokenMessageCompleter**: 消息格式转换和渲染管理
- **Renderer**: 负责将对话格式转换为模型可理解的 token 序列

## ⚙️ 配置说明 (Configuration)

### 模型配置格式

服务使用特殊的模型标识符格式：`topic$renderer`

**格式说明**:

- `topic`: 模型快照的路径，不需要 `az://` 前缀
- `renderer`: 渲染器名称，用于消息格式转换
- 分隔符: `$` 用于分隔 topic 和 renderer

**示例**:

```
bus:snap:orngcresco/twapi/mini/e/yunshengli-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-rrbif-v1/policy/step_000300:user:xuga$harmony_v4.0.16
```

### BusTokenCompleter 配置

- **QoS 类型**: 默认使用 `QoSType.ROUND_ROBIN_BY_POD` 进行负载均衡
- **温度参数**: 默认 temperature = 1.0
- **重试机制**: 支持自动重试失败的请求
- **缓存机制**: 组件级别的缓存，避免重复初始化

### 服务配置

- **主机地址**: 默认 `0.0.0.0` (监听所有接口)
- **端口**: 默认 `8500`
- **日志级别**: 默认 `info`
- **自动重载**: 开发模式下启用 `--reload`

## 🚀 使用方法 (Usage)

### 客户端配置示例

#### Python OpenAI SDK

```python
from openai import OpenAI

# 配置客户端使用本地代理
client = OpenAI(
    base_url="http://localhost:8500/v1",
    api_key="dummy"  # 本地服务不需要真实 API key
)

# 发送请求
response = client.chat.completions.create(
    model="your_topic$your_renderer",
    messages=[
        {"role": "user", "content": "Hello, world!"}
    ]
)

print(response.choices[0].message.content)
```

#### 流式响应

```python
stream = client.chat.completions.create(
    model="your_topic$your_renderer",
    messages=[
        {"role": "user", "content": "Tell me a story"}
    ],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
```

## 🔍 API 规范

### 请求格式

遵循 OpenAI Chat Completions API v1 规范：

```json
{
  "model": "topic$renderer",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello!"}
  ],
  "tools": [...],           // 可选：工具定义
  "tool_choice": "auto",    // 可选：工具选择策略
  "stream": false,          // 可选：是否流式响应
  "temperature": 1.0,       // 可选：采样温度
  "max_tokens": null,       // 可选：最大 token 数
  "top_p": 1.0,            // 可选：nucleus 采样参数
  "n": 1,                  // 可选：生成选择数量
  "stop": null             // 可选：停止序列
}
```

### 响应格式

#### 非流式响应

```json
{
  "id": "chatcmpl-...",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "topic$renderer",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you?",
        "tool_calls": null
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30
  }
}
```

#### 流式响应

```
data: {"id":"chatcmpl-...","object":"chat.completion.chunk","created":1234567890,"model":"topic$renderer","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chatcmpl-...","object":"chat.completion.chunk","created":1234567890,"model":"topic$renderer","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-...","object":"chat.completion.chunk","created":1234567890,"model":"topic$renderer","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

本项目遵循内部许可协议。
